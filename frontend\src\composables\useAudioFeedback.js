/**
 * 音频反馈系统
 * 为训练过程提供语音提示和音效反馈
 */

import { ref } from 'vue'

export function useAudioFeedback() {
  const isEnabled = ref(true)
  const volume = ref(0.7)
  const speechSynthesis = window.speechSynthesis
  const audioContext = ref(null)

  // 初始化音频上下文
  const initAudioContext = () => {
    if (!audioContext.value && window.AudioContext) {
      audioContext.value = new (window.AudioContext || window.webkitAudioContext)()
    }
  }

  /**
   * 语音播报
   * @param {string} text - 要播报的文本
   * @param {Object} options - 语音选项
   */
  const speak = (text, options = {}) => {
    if (!isEnabled.value || !speechSynthesis) return

    // 停止当前播报
    speechSynthesis.cancel()

    const utterance = new SpeechSynthesisUtterance(text)
    utterance.lang = options.lang || 'zh-CN'
    utterance.volume = options.volume || volume.value
    utterance.rate = options.rate || 1.0
    utterance.pitch = options.pitch || 1.0

    // 尝试使用中文语音
    const voices = speechSynthesis.getVoices()
    const chineseVoice = voices.find(voice => 
      voice.lang.includes('zh') || voice.name.includes('Chinese')
    )
    if (chineseVoice) {
      utterance.voice = chineseVoice
    }

    speechSynthesis.speak(utterance)
    console.log('[AudioFeedback] 播报:', text)
  }

  /**
   * 播放提示音
   * @param {string} type - 音效类型
   */
  const playSound = (type) => {
    if (!isEnabled.value) return

    initAudioContext()
    if (!audioContext.value) return

    const ctx = audioContext.value
    const oscillator = ctx.createOscillator()
    const gainNode = ctx.createGain()

    oscillator.connect(gainNode)
    gainNode.connect(ctx.destination)

    // 根据类型设置音效参数
    switch (type) {
      case 'success':
        // 成功音：上升音调
        oscillator.frequency.setValueAtTime(523.25, ctx.currentTime) // C5
        oscillator.frequency.exponentialRampToValueAtTime(783.99, ctx.currentTime + 0.3) // G5
        gainNode.gain.setValueAtTime(volume.value * 0.3, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.3)
        oscillator.start(ctx.currentTime)
        oscillator.stop(ctx.currentTime + 0.3)
        break

      case 'warning':
        // 警告音：短促的中音
        oscillator.frequency.setValueAtTime(440, ctx.currentTime) // A4
        gainNode.gain.setValueAtTime(volume.value * 0.2, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.2)
        oscillator.start(ctx.currentTime)
        oscillator.stop(ctx.currentTime + 0.2)
        break

      case 'error':
        // 错误音：下降音调
        oscillator.frequency.setValueAtTime(349.23, ctx.currentTime) // F4
        oscillator.frequency.exponentialRampToValueAtTime(196, ctx.currentTime + 0.4) // G3
        gainNode.gain.setValueAtTime(volume.value * 0.3, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.4)
        oscillator.start(ctx.currentTime)
        oscillator.stop(ctx.currentTime + 0.4)
        break

      case 'start':
        // 开始音：简单的提示音
        oscillator.frequency.setValueAtTime(523.25, ctx.currentTime) // C5
        gainNode.gain.setValueAtTime(volume.value * 0.2, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.15)
        oscillator.start(ctx.currentTime)
        oscillator.stop(ctx.currentTime + 0.15)
        break

      default:
        // 默认提示音
        oscillator.frequency.setValueAtTime(440, ctx.currentTime)
        gainNode.gain.setValueAtTime(volume.value * 0.1, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.1)
        oscillator.start(ctx.currentTime)
        oscillator.stop(ctx.currentTime + 0.1)
    }
  }

  /**
   * 根据训练状态提供音频反馈
   * @param {string} state - 训练状态
   * @param {number} score - 当前分数
   * @param {string} feedback - 反馈文本
   */
  const provideFeedback = (state, score, feedback) => {
    if (!isEnabled.value) return

    switch (state) {
      case 'COMPLETED':
        playSound('success')
        if (score >= 90) {
          speak('太棒了！动作完成得非常好！')
        } else if (score >= 70) {
          speak('很好！动作完成了！')
        } else {
          speak('动作完成，继续加油！')
        }
        break

      case 'MOVING_TO_TARGET':
        // 不播放音效，避免干扰
        break

      case 'HOLDING':
        if (Math.random() < 0.3) { // 30%概率播放鼓励
          speak('保持住，很好！')
        }
        break

      case 'RETURNING':
        // 不播放音效
        break

      case 'ERROR':
        playSound('error')
        speak('请调整姿势，重新开始')
        break

      case 'IDLE':
        if (feedback && feedback.includes('准备')) {
          playSound('start')
        }
        break
    }
  }

  /**
   * 播报动作指导
   * @param {string} actionType - 动作类型
   * @param {string} side - 侧别
   */
  const announceAction = (actionType, side) => {
    if (!isEnabled.value) return

    const sideText = side === 'left' ? '左侧' : '右侧'
    let actionText = ''

    switch (actionType) {
      case 'shoulder_touch':
        actionText = `${sideText}对侧触肩动作`
        break
      case 'arm_raise':
        actionText = `${sideText}手臂上举动作`
        break
      case 'finger_touch':
        actionText = `${sideText}指尖对触动作`
        break
      case 'palm_flip':
        actionText = `${sideText}手掌翻转动作`
        break
      default:
        actionText = `${sideText}康复动作`
    }

    speak(`准备开始${actionText}，请按照视频示范进行练习`)
  }

  /**
   * 设置音量
   * @param {number} newVolume - 新音量 (0-1)
   */
  const setVolume = (newVolume) => {
    volume.value = Math.max(0, Math.min(1, newVolume))
  }

  /**
   * 切换音频开关
   */
  const toggleAudio = () => {
    isEnabled.value = !isEnabled.value
    if (isEnabled.value) {
      speak('音频反馈已开启')
    }
  }

  /**
   * 停止所有音频
   */
  const stopAll = () => {
    if (speechSynthesis) {
      speechSynthesis.cancel()
    }
  }

  return {
    // 状态
    isEnabled,
    volume,

    // 方法
    speak,
    playSound,
    provideFeedback,
    announceAction,
    setVolume,
    toggleAudio,
    stopAll
  }
}
