/**
 * 手臂上举动作检测器
 * 检测用户将手臂从体侧向上举起的动作
 */

import { utils } from '@/utils/geometryUtils'

export class ArmRaiseDetector {
  constructor(side = 'left', level = 'medium') {
    this.side = side
    this.level = level
    
    // 根据侧别设置关键点索引
    if (this.side === 'left') {
      this.wristIdx = 9  // LEFT_WRIST
      this.elbowIdx = 7  // LEFT_ELBOW
      this.shoulderIdx = 5  // LEFT_SHOULDER
    } else {
      this.wristIdx = 10  // RIGHT_WRIST
      this.elbowIdx = 8   // RIGHT_ELBOW
      this.shoulderIdx = 6   // RIGHT_SHOULDER
    }
    
    // 根据难度设置参数
    this.config = this.getDifficultyConfig(level)
    
    // 初始化状态
    this.reset()
  }
  
  getDifficultyConfig(level) {
    switch (level) {
      case 'easy':
        return { 
          targetAngle: 120,  // 目标角度（度）
          holdDuration: 1000,  // 保持时间（毫秒）
          angleThreshold: 15   // 角度容差
        }
      case 'hard':
        return { 
          targetAngle: 160, 
          holdDuration: 3000, 
          angleThreshold: 10 
        }
      case 'medium':
      default:
        return { 
          targetAngle: 140, 
          holdDuration: 2000, 
          angleThreshold: 12 
        }
    }
  }
  
  reset() {
    this.state = 'IDLE'
    this.score = 0
    this.feedback = `准备将${this.side === 'left' ? '左' : '右'}手臂向上举起`
    
    // 内部状态
    this.holdStartTime = 0
    this.initialAngle = 0
    this.maxAngleReached = 0
  }
  
  /**
   * 更新检测器状态
   * @param {Array} keypoints - 关键点数据
   * @returns {Object} 检测结果
   */
  update(keypoints) {
    if (this.state === 'COMPLETED') {
      return { 
        success: true,
        state: this.state, 
        score: this.score, 
        feedback: this.feedback 
      }
    }
    
    // 数据提取和验证
    const getPoint = (idx) => ({ 
      x: keypoints[idx][0], 
      y: keypoints[idx][1], 
      c: keypoints[idx][2] 
    })
    
    const wrist = getPoint(this.wristIdx)
    const elbow = getPoint(this.elbowIdx)
    const shoulder = getPoint(this.shoulderIdx)
    
    const confidenceThreshold = 0.2
    if (wrist.c < confidenceThreshold || elbow.c < confidenceThreshold || shoulder.c < confidenceThreshold) {
      return {
        success: false,
        state: this.state,
        score: this.score,
        feedback: '请保证手臂关键部位在画面中'
      }
    }
    
    // 计算手臂角度（肩膀-肘部-手腕的角度）
    const armAngle = utils.calculateAngle(shoulder, elbow, wrist)
    
    // 计算手臂相对于身体的抬起角度
    const shoulderToWrist = { x: wrist.x - shoulder.x, y: wrist.y - shoulder.y }
    const raiseAngle = Math.abs(Math.atan2(-shoulderToWrist.y, Math.abs(shoulderToWrist.x)) * 180 / Math.PI)
    
    // 更新最大角度记录
    this.maxAngleReached = Math.max(this.maxAngleReached, raiseAngle)
    
    // 状态机逻辑
    switch (this.state) {
      case 'IDLE':
        // 检测开始抬起
        if (raiseAngle > 20) {  // 开始抬起的阈值
          this.state = 'RAISING'
          this.initialAngle = raiseAngle
          this.feedback = '很好，继续向上举起手臂...'
        }
        break
        
      case 'RAISING':
        // 分数范围: 0 -> 60
        const raiseProgress = Math.min(1, raiseAngle / this.config.targetAngle)
        this.score = Math.max(0, Math.min(60, 60 * raiseProgress))
        this.feedback = `继续举起... 当前角度: ${Math.round(raiseAngle)}°`
        
        // 检查是否达到目标角度
        if (raiseAngle >= this.config.targetAngle - this.config.angleThreshold) {
          this.state = 'HOLDING'
          this.holdStartTime = Date.now()
          this.score = 60
          this.feedback = `太棒了！请保持 ${this.config.holdDuration / 1000} 秒`
        }
        break
        
      case 'HOLDING':
        // 分数范围: 60 -> 90
        const holdingTime = Date.now() - this.holdStartTime
        
        // 检查是否仍在目标位置
        if (raiseAngle < this.config.targetAngle - this.config.angleThreshold * 1.5) {
          this.state = 'RAISING'
          this.feedback = '请重新举起手臂到目标位置'
          break
        }
        
        // 根据保持时间计算分数
        const holdProgress = Math.min(1, holdingTime / this.config.holdDuration)
        this.score = 60 + 30 * holdProgress
        
        if (holdProgress >= 1) {
          this.state = 'LOWERING'
          this.score = 90
          this.feedback = '很好！现在缓慢放下手臂'
        } else {
          this.feedback = `保持... 剩余 ${Math.ceil((this.config.holdDuration - holdingTime) / 1000)} 秒`
        }
        break
        
      case 'LOWERING':
        // 分数范围: 90 -> 100
        const lowerProgress = Math.max(0, 1 - (raiseAngle / this.maxAngleReached))
        this.score = 90 + 10 * lowerProgress
        this.feedback = '正在放下手臂...'
        
        // 检查是否回到起始位置
        if (raiseAngle < 30) {  // 回到较低位置
          this.state = 'COMPLETED'
          this.score = 100
          this.feedback = `动作完成！最终得分: ${Math.round(this.score)}`
        }
        break
    }
    
    return {
      success: true,
      state: this.state,
      score: Math.round(this.score),
      feedback: this.feedback
    }
  }
}

// 默认导出
export default ArmRaiseDetector
