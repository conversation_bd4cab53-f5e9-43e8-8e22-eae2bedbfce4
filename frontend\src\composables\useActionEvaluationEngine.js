/**
 * 动作评估引擎
 * 根据动作类型、侧别和难度级别动态加载和管理评估器
 */

import { ref, computed } from 'vue'
import ShoulderTouchLogic from '@/composables/gemini/ShoulderTouchLogic'
import ArmRaiseDetector from '@/composables/detectors/armRaiseDetector'

export function useActionEvaluationEngine() {
  // 当前评估器实例
  const currentDetector = ref(null)
  const currentActionType = ref(null)
  const isActive = ref(false)
  
  // 评估结果状态
  const evaluationResult = ref({
    state: 'IDLE',
    score: 0,
    feedback: '准备开始训练',
    success: true
  })

  /**
   * 加载指定动作的评估器
   * @param {string} actionType - 动作类型 (shoulder_touch, arm_raise, finger_touch, palm_flip)
   * @param {string} side - 侧别 (left, right)
   * @param {string} level - 难度级别 (easy, medium, hard)
   */
  const loadDetector = (actionType, side = 'left', level = 'medium') => {
    console.log(`[EvaluationEngine] 加载检测器: ${actionType}, 侧别: ${side}, 难度: ${level}`)
    
    // 重置之前的检测器
    if (currentDetector.value) {
      currentDetector.value = null
    }
    
    try {
      switch (actionType) {
        case 'shoulder_touch':
          currentDetector.value = new ShoulderTouchLogic(side, level)
          break
        case 'arm_raise':
          currentDetector.value = new ArmRaiseDetector(side, level)
          break
        case 'finger_touch':
          // 暂时使用ShoulderTouchLogic作为占位符
          currentDetector.value = new ShoulderTouchLogic(side, level)
          console.warn('[EvaluationEngine] finger_touch检测器暂未实现，使用shoulder_touch作为占位符')
          break
        case 'palm_flip':
          // 暂时使用ShoulderTouchLogic作为占位符
          currentDetector.value = new ShoulderTouchLogic(side, level)
          console.warn('[EvaluationEngine] palm_flip检测器暂未实现，使用shoulder_touch作为占位符')
          break
        default:
          throw new Error(`未知的动作类型: ${actionType}`)
      }
      
      currentActionType.value = actionType
      isActive.value = true
      
      // 初始化评估结果
      evaluationResult.value = {
        state: 'IDLE',
        score: 0,
        feedback: `准备开始${getActionDisplayName(actionType)}训练`,
        success: true
      }
      
      console.log(`[EvaluationEngine] ${actionType}检测器加载成功`)
      return true
      
    } catch (error) {
      console.error('[EvaluationEngine] 检测器加载失败:', error)
      isActive.value = false
      return false
    }
  }

  /**
   * 更新评估器（处理实时姿态数据）
   * @param {Array} keypoints - 133个关键点数据 [[x, y, confidence], ...]
   */
  const updateEvaluation = (keypoints) => {
    if (!currentDetector.value || !isActive.value) {
      return evaluationResult.value
    }
    
    try {
      console.log(keypoints)
      const result = currentDetector.value.update(keypoints)
      evaluationResult.value = {
        state: result.state || 'IDLE',
        score: result.score || 0,
        feedback: result.feedback || '评估中...',
        success: result.success !== false
      }
      
      return evaluationResult.value
    } catch (error) {
      console.error('[EvaluationEngine] 评估更新失败:', error)
      evaluationResult.value = {
        state: 'ERROR',
        score: 0,
        feedback: result.feedback || "请确保关键部位在画面中",
        success: false
      }
      return evaluationResult.value
    }
  }

  /**
   * 重置当前评估器
   */
  const resetDetector = () => {
    if (currentDetector.value && typeof currentDetector.value.reset === 'function') {
      currentDetector.value.reset()
      evaluationResult.value = {
        state: 'IDLE',
        score: 0,
        feedback: `准备开始${getActionDisplayName(currentActionType.value)}训练`,
        success: true
      }
      console.log('[EvaluationEngine] 检测器已重置')
    }
  }

  /**
   * 停止评估
   */
  const stopEvaluation = () => {
    isActive.value = false
    currentDetector.value = null
    currentActionType.value = null
    evaluationResult.value = {
      state: 'IDLE',
      score: 0,
      feedback: '评估已停止',
      success: true
    }
    console.log('[EvaluationEngine] 评估已停止')
  }

  /**
   * 获取动作显示名称
   */
  const getActionDisplayName = (actionType) => {
    const names = {
      'shoulder_touch': '对侧触肩',
      'arm_raise': '手臂上举',
      'finger_touch': '指尖对触',
      'palm_flip': '手掌翻转'
    }
    return names[actionType] || actionType
  }

  // 计算属性
  const isCompleted = computed(() => evaluationResult.value.state === 'COMPLETED')
  const currentScore = computed(() => evaluationResult.value.score)
  const currentFeedback = computed(() => evaluationResult.value.feedback)
  const currentState = computed(() => evaluationResult.value.state)

  return {
    // 状态
    isActive,
    currentActionType,
    evaluationResult,
    
    // 计算属性
    isCompleted,
    currentScore,
    currentFeedback,
    currentState,
    
    // 方法
    loadDetector,
    updateEvaluation,
    resetDetector,
    stopEvaluation,
    getActionDisplayName
  }
}
